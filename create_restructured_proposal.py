#!/usr/bin/env python3
"""
Create a restructured MJO proposal with improved visual structure
"""

import docx
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn

def add_heading_with_style(doc, text, level=1):
    """Add a heading with consistent styling"""
    heading = doc.add_heading(text, level=level)
    heading.alignment = WD_ALIGN_PARAGRAPH.LEFT
    return heading

def add_bullet_point(doc, text, indent_level=0):
    """Add a bullet point with proper indentation"""
    para = doc.add_paragraph(text, style='List Bullet')
    para.paragraph_format.left_indent = Inches(0.25 * (indent_level + 1))
    return para

def add_numbered_point(doc, text, indent_level=0):
    """Add a numbered point with proper indentation"""
    para = doc.add_paragraph(text, style='List Number')
    para.paragraph_format.left_indent = Inches(0.25 * (indent_level + 1))
    return para

def create_restructured_proposal():
    # Create new document
    doc = Document()
    
    # Title section
    title_para = doc.add_paragraph()
    title_run = title_para.add_run("Proposal Acronym: GRAVMO")
    title_run.bold = True
    title_run.font.size = Pt(14)
    title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    subtitle_para = doc.add_paragraph()
    subtitle_run = subtitle_para.add_run("Modulation of Stratospheric Gravity Waves by the Madden-Julian Oscillation: Observational Evidence from Radio Occultation")
    subtitle_run.bold = True
    subtitle_run.font.size = Pt(12)
    subtitle_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Basic info
    doc.add_paragraph("Type of Action: MSCA Postdoctoral Fellowship (PF-GF)", style='Normal').alignment = WD_ALIGN_PARAGRAPH.CENTER
    doc.add_paragraph("Researcher: Toyese Tunde Ayorinde, Ph.D", style='Normal').alignment = WD_ALIGN_PARAGRAPH.CENTER
    doc.add_paragraph("Supervisor: RNDr. Petr Šácha, Ph.D", style='Normal').alignment = WD_ALIGN_PARAGRAPH.CENTER
    doc.add_paragraph("Host Institution: Charles University, Faculty of Mathematics and Physics, Czech Republic", style='Normal').alignment = WD_ALIGN_PARAGRAPH.CENTER
    doc.add_paragraph("Call: HORIZON-MSCA-202X-PF-01", style='Normal').alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_page_break()
    
    # Abstract
    add_heading_with_style(doc, "Proposal Abstract: GRAVMO", 1)
    
    abstract_points = [
        "Understanding tropical-stratospheric coupling through atmospheric gravity waves (GWs) is crucial for improving subseasonal-to-seasonal forecasts and climate models¹,².",
        "The Madden-Julian Oscillation (MJO) modulates tropical GW activity³,⁴,⁵, yet this modulation remains underrepresented in climate models.",
        "Current climate models struggle with tropical intraseasonal variability⁶, partly due to inadequate GW process representation.",
        "This project leverages Global Navigation Satellite System Radio Occultation (RO) data to address these shortcomings.",
        "Goal: Deliver comprehensive, observationally-derived global characterization of stratospheric GWs modulated by the MJO for climate model validation."
    ]
    
    for point in abstract_points:
        add_bullet_point(doc, point)
    
    # Research Objectives
    add_heading_with_style(doc, "Research Objectives", 2)
    
    objectives = [
        "**O1. Validation of RO-derived GW Parameters (WP1):** Establish comprehensive validation framework through process-based case studies and statistical comparisons with independent satellite measurements.",
        "**O2. MJO-GW Modulation Climatology (WP2):** Quantify and assess MJO's modulation of GW activity, producing novel climatology composited by MJO's distinct propagation types.",
        "**O3. Climate Model Evaluation (WP3):** Evaluate how state-of-the-art climate models represent MJO-GW interactions using observational climatology as benchmark."
    ]
    
    for i, obj in enumerate(objectives, 1):
        add_numbered_point(doc, obj)
    
    # Excellence Section
    add_heading_with_style(doc, "Excellence", 1)
    
    add_heading_with_style(doc, "Scientific Innovation & Beyond State-of-the-Art", 2)
    
    innovation_points = [
        "**Unique Dataset:** Nearly two decades (2006-2024) of multi-mission RO data with unprecedented spatiotemporal coverage",
        "**Novel Approach:** First systematic global investigation of MJO-modulated stratospheric GWs",
        "**Advanced Methodology:** Categorization by MJO propagation types (standing, jumping, slow/fast-propagating)",
        "**Critical Gap:** Addresses missing observational constraints for GW parameterizations in climate models"
    ]
    
    for point in innovation_points:
        add_bullet_point(doc, point)
    
    # Scientific Background (restructured)
    add_heading_with_style(doc, "Scientific Background", 2)
    
    add_heading_with_style(doc, "MJO-Stratosphere Coupling", 3)
    background_mjo = [
        "MJO is the primary driver of tropical intraseasonal variability⁷,⁸",
        "Influences global weather through complex teleconnections⁵,⁹",
        "Modulated by QBO and ENSO interactions⁵,¹³",
        "Critical for subseasonal-to-seasonal prediction"
    ]
    for point in background_mjo:
        add_bullet_point(doc, point)
    
    add_heading_with_style(doc, "Gravity Waves Role", 3)
    background_gw = [
        "Medium-scale oscillations (horizontal: tens-thousands km, vertical: several km)",
        "Drive key circulations: Brewer-Dobson Circulation and QBO¹,²",
        "Critical vertical coupling pathway for MJO teleconnections",
        "Generated by MJO-modulated deep convection"
    ]
    for point in background_gw:
        add_bullet_point(doc, point)
    
    add_heading_with_style(doc, "Knowledge Gaps", 3)
    gaps = [
        "Limited global coverage and long-term perspective in previous studies",
        "Poor understanding of QBO/ENSO modulation of MJO-GW interactions",
        "Inadequate observational constraints for climate model parameterizations",
        "Climate models fail to reproduce observed QBO-MJO connections⁶,⁷"
    ]
    for point in gaps:
        add_bullet_point(doc, point)
    
    # Methodology Section (restructured)
    add_heading_with_style(doc, "Methodology", 2)

    add_heading_with_style(doc, "Data Sources", 3)
    data_sources = [
        "**RO Data:** Multi-mission Level 2 'dry' temperature profiles (COSMIC-1/2, MetOp, Spire)",
        "**Reanalysis:** ERA5 daily/monthly fields (winds, T, Z, q, SLP, SAT)",
        "**Climate Indices:** Real-time Multivariate MJO Index, QBO index, ENSO index",
        "**Satellite Data:** OLR data (NOAA), solar activity indices",
        "**Model Data:** CMIP6/7 outputs with adequate stratospheric resolution"
    ]
    for point in data_sources:
        add_bullet_point(doc, point)

    add_heading_with_style(doc, "Analysis Framework", 3)

    # WP1 Methodology
    add_heading_with_style(doc, "WP1: Validation Framework", 4)
    wp1_methods = [
        "Intercomparison with TIMED/SABER and Aura/MLS observations",
        "Process-based case studies of documented GW events",
        "Statistical validation using correlation, bias, RMSE metrics",
        "GW parameter extraction: potential energy, momentum flux, vertical wavelength"
    ]
    for point in wp1_methods:
        add_bullet_point(doc, point, 1)

    # WP2 Methodology
    add_heading_with_style(doc, "WP2: MJO-GW Climatology", 4)
    wp2_methods = [
        "Composite analysis by MJO phase and amplitude (RMM index)",
        "Categorization by MJO propagation types (standing, jumping, slow/fast)",
        "Global and seasonal distribution analysis",
        "Statistical significance testing of GW anomalies"
    ]
    for point in wp2_methods:
        add_bullet_point(doc, point, 1)

    # WP3 Methodology
    add_heading_with_style(doc, "WP3: Model Evaluation", 4)
    wp3_methods = [
        "CMIP6/7 model output analysis",
        "MJO composite analysis on parameterized GW momentum fluxes",
        "Systematic bias identification in GW parameterizations",
        "Performance assessment using observational benchmarks"
    ]
    for point in wp3_methods:
        add_bullet_point(doc, point, 1)

    # Impact Section
    add_heading_with_style(doc, "Impact", 1)

    add_heading_with_style(doc, "Scientific Impact", 2)
    sci_impact = [
        "**Landmark Dataset:** First global, long-term observational characterization of MJO-modulated GWs",
        "**Model Improvement:** Critical benchmarks for evaluating and improving GW parameterizations",
        "**Fundamental Insights:** New understanding of stratosphere-troposphere coupling mechanisms",
        "**Community Resource:** Open-access datasets for broader scientific community"
    ]
    for point in sci_impact:
        add_bullet_point(doc, point)

    add_heading_with_style(doc, "Societal & Economic Impact", 2)
    societal_impact = [
        "Enhanced subseasonal-to-seasonal forecasting capabilities",
        "Improved climate projections for policy and adaptation planning",
        "Benefits for climate-sensitive sectors (agriculture, energy, water resources)",
        "Reduced economic losses from extreme weather events"
    ]
    for point in societal_impact:
        add_bullet_point(doc, point)

    return doc

if __name__ == "__main__":
    doc = create_restructured_proposal()
    doc.save('MJO_proposal_TTA_Restructured_Part1.docx')
    print("Created restructured proposal (Part 1): MJO_proposal_TTA_Restructured_Part1.docx")
