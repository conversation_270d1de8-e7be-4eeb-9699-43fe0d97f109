=== INTEREST STATEMENT EXAMPLE ===

Interest   Statement <he condensed matter limit re6uires a different level of description. MB proposal aims to complement the StopIdoCnS approach Cith a SHottomIupS description Chich   can   descriHe   E=S   in   the   loC   and   moderate   temperatures   ranFe7   i.e.   near   the relevant solidIsolid and solidIli6uid transitions. <he   desiFn   of capsules   for   *nertiallB   Confined   Fusion   D*CFE durinF   the   National *Fnition   CampaiFn   DN*CE   is   a   clear   eGample   of   an   application   in   need   for   a   Food SHottomIupS description for a specific set of materials. <he capsule desiFn is driven HB   the   need   of   avoidinF   hBdrodBnamic   instaHilities   upon   the   first   staFes   of   shock compression. <here are several candidate materials for makinF these iFnition  capsules7   amonF   them7   NerBllium   and   HiFh   DensitB   CarHon   DHDCE.   Due   to   the   specific properties of each material7 the current strateFies for avoidinF hBdrodBnamic   instaHilities   are   someChat   different.   *n   the   case   of   NerBllium7   it   Cill He   necessarB   to   ensure   that   the   first   shock   completelB   melts   the   capsule   materialT Chile in the case of HDC the approach is to produce a first shock that can siFnificantlB   compress   and   heat   the   material   Cithout   meltinF   it.   Note   that   in   order   to achieve   these   desiFn   Foals7   one   must   have   a   precise   knoCledFe   of   the   phase   diaFram and the E=S of the materials involved. F9=M   *DEAR   <=   9EAR MB   proposal   focuses   on   the   implementation   of   an   inteFral   approach   for   elucidatinF the   properties   of   materials   and   their   E=S.   *n   particular7   *   propose   to   use   a comHination of first principles calculations to Five an overall description of the condensed   matter   domain7   includinF   solidIsolid   transitions   at   finite   temperature7 and meltinF lines. A keB component of the E=S is the one correspondinF to the solid phases. <he proposed approach Cill alloC the computation of E=S cold curves that include full  ionic   6uantum   effects   DUero   point   enerFBE7   Chich   are   critical7   for   eGample7   Chen  descriHinF the loC temperature initial condition in shock eGperiments. Previous Cork demonstrated that it is also feasiHle to eGtract anharmonic effects and its implications to the E=S from first principles molecular dBnamics. MeltinF lines can He computed HB the tCoIphase method. <he tCoIphase method is Hased   on   the   direct   simulation   of   the   solidification   and   meltinF   processes   HB   startinF from an initial condition that has Hoth the solid and the li6uid in the same  simulation cell.   NB folloCinF the evolution of the solidIli6uid interface at a Fiven temperature   and   pressure7   one can determine Chich   is the   most staHle phase   under such conditions. =f note is that this scheme does not re6uire the direct calculation of  the   free   enerFB7   Hut   it   can   He   of   paramount   utilitB   for   calculatinF   it7   as   *   Cill   descriHe  later.   *n   principle7   there   is   no   intrinsic   limitation   in   this   approach7   as   lonF   as   the staHle solid phases are knoCn. <he staHle solid structure can He inferred from eGperimental   results7   or   it   can   He   solved   in   a   purelB   aH   initio   conteGt. FindinF   the   staHle   solid   phase   at   a   Fiven   pressure   and   temperature   is   a   hard DcomHinatorialE   proHlem   that   has   Heen   unsolved   for   manB   Bears.   <he multidimensional nature of the proHlem and the eGistence of local minima has motivated   the   use   of   a   numHer   of   different   stochastic   FloHal   search   strateFies.   For  eGample7   =Fanov   et   al.   recentlB   demonstrated   that   evolutionarB   alForithms   can   He  used   to   predict   the   crBstal   structure   on   sBstems   Cith   up   to   VW   atoms   per   unit   cell7 includinF compounds and multiIstoichiometric alloBs. *t   is   possiHle   to   make   the   alForithm   concentrate   on   the   most   promisinF   area   of   the confiFuration   space   until   the   FloHal   minimum   is   found7   HB   selectinF   onlB   the   Hest individuals   Dless   enerFeticE   in   a   fictitious   population   of   structures7   and   comHininF them   to   produce   the   neGt   Feneration   of   structures.   *n   order   to   increase   efficiencB7   * propose   to   use   multiple   levels   of   theoretical   approGimations   to   the   total   enerFBT   as the alForithm approaches the minimum it can sCitch to more accurate methods of calculatinF   the   enerFB.   Xe   can   incorporate   alForithms   of   this   kind   in   this   proJect   to predict the crBstal structure in cases Chere eGperimental crBstalloFraphic information is not availaHle. =nce the meltinF lines for each staHle solid phase are determined De.F. HB the tCoI phase   methodE7   theB   