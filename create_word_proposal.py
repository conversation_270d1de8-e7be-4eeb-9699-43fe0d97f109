#!/usr/bin/env python3
"""
Create a Word document (.docx) for the MJO proposal with specified formatting:
- Font size: 10pt
- Line spacing: 1.15
- Structure: Background and Motivation, Proposed Research and Scientific Questions, 
  Methodology, Relevance to NCAR's Research and Strategic Plan, 
  Relevance of Previous Research Experience, References
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT, WD_LINE_SPACING
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn

def add_superscript_reference(paragraph, ref_numbers):
    """Add superscript reference numbers to a paragraph"""
    run = paragraph.add_run(ref_numbers)
    run.font.superscript = True
    run.font.size = Pt(8)

def create_mjo_proposal():
    # Create a new document
    doc = Document()
    
    # Set up document margins (2.0 cm = 0.79 inches)
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(0.79)
        section.bottom_margin = Inches(0.79)
        section.left_margin = Inches(0.79)
        section.right_margin = Inches(0.79)
    
    # Create custom styles
    styles = doc.styles
    
    # Title style
    title_style = styles.add_style('CustomTitle', WD_STYLE_TYPE.PARAGRAPH)
    title_style.font.name = 'Times New Roman'
    title_style.font.size = Pt(12)
    title_style.font.bold = True
    title_style.paragraph_format.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    title_style.paragraph_format.line_spacing_rule = WD_LINE_SPACING.MULTIPLE
    title_style.paragraph_format.line_spacing = 1.15
    title_style.paragraph_format.space_after = Pt(12)
    
    # Author style
    author_style = styles.add_style('CustomAuthor', WD_STYLE_TYPE.PARAGRAPH)
    author_style.font.name = 'Times New Roman'
    author_style.font.size = Pt(10)
    author_style.paragraph_format.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    author_style.paragraph_format.line_spacing_rule = WD_LINE_SPACING.MULTIPLE
    author_style.paragraph_format.line_spacing = 1.15
    author_style.paragraph_format.space_after = Pt(18)
    
    # Heading style
    heading_style = styles.add_style('CustomHeading', WD_STYLE_TYPE.PARAGRAPH)
    heading_style.font.name = 'Times New Roman'
    heading_style.font.size = Pt(11)
    heading_style.font.bold = True
    heading_style.paragraph_format.line_spacing_rule = WD_LINE_SPACING.MULTIPLE
    heading_style.paragraph_format.line_spacing = 1.15
    heading_style.paragraph_format.space_before = Pt(12)
    heading_style.paragraph_format.space_after = Pt(6)
    
    # Body text style
    body_style = styles.add_style('CustomBody', WD_STYLE_TYPE.PARAGRAPH)
    body_style.font.name = 'Times New Roman'
    body_style.font.size = Pt(10)
    body_style.paragraph_format.alignment = WD_PARAGRAPH_ALIGNMENT.JUSTIFY
    body_style.paragraph_format.line_spacing_rule = WD_LINE_SPACING.MULTIPLE
    body_style.paragraph_format.line_spacing = 1.15
    body_style.paragraph_format.space_after = Pt(6)
    
    # Add title
    title = doc.add_paragraph('Modulation of Stratospheric Gravity Waves by the Madden-Julian Oscillation: Observational Evidence from Radio Occultation', style='CustomTitle')
    
    # Add author information
    author = doc.add_paragraph('Dr. Toyese Tunde Ayorinde', style='CustomAuthor')
    author.add_run('\nNCAR Postdoctoral Fellowship Proposal')
    author.add_run('\nHost Institution: National Center for Atmospheric Research (NCAR)')
    author.add_run('\nSupervisor: Dr. [Supervisor Name]')
    
    # Background and Motivation
    heading1 = doc.add_paragraph('Background and Motivation', style='CustomHeading')
    
    p1 = doc.add_paragraph('The Madden-Julian Oscillation (MJO) is the dominant mode of tropical intraseasonal variability, characterized by eastward-propagating large-scale coupled patterns of convection and circulation with periods of 30-90 days', style='CustomBody')
    add_superscript_reference(p1, '1,2')
    p1.add_run('. The MJO exerts profound influence on global weather patterns through complex teleconnections, affecting storm tracks, cyclone activity, atmospheric rivers, and blocking events across the Northern Hemisphere')
    add_superscript_reference(p1, '3,4,5')
    p1.add_run('. Understanding the coupling between the tropical troposphere and the global stratosphere via atmospheric gravity waves (GWs) is crucial for climate modeling and prediction')
    add_superscript_reference(p1, '6,7')
    p1.add_run('.')
    
    p2 = doc.add_paragraph('Atmospheric gravity waves, generated primarily by MJO-modulated deep convection, represent a critical vertical coupling pathway that drives key middle atmosphere circulations including the Brewer-Dobson Circulation (BDC) and contributes to the Quasi-Biennial Oscillation (QBO)', style='CustomBody')
    add_superscript_reference(p2, '6,8')
    p2.add_run('. Recent studies have highlighted important connections between the MJO, QBO, and solar activity, with the QBO\'s influence on MJO teleconnections operating through multiple pathways, including modulation of the background stratospheric flow that affects both Rossby wave propagation and GW filtering')
    add_superscript_reference(p2, '9,10')
    p2.add_run('.')
    
    p3 = doc.add_paragraph('Despite significant advances, current understanding suffers from several critical limitations: (i) Previous observational studies often lack the necessary global coverage, vertical resolution, or long-term perspective to robustly characterize the MJO\'s impact on GWs globally. (ii) How the MJO\'s impact on GWs is interactively modulated by dominant climate modes like the QBO and ENSO remains poorly constrained observationally on a global scale', style='CustomBody')
    add_superscript_reference(p3, '9,11')
    p3.add_run('. (iii) Global climate models struggle to realistically simulate the QBO')
    add_superscript_reference(p3, '12')
    p3.add_run(' and MJO teleconnections')
    add_superscript_reference(p3, '10')
    p3.add_run(', with most models showing significantly weaker QBO modulation of MJO activity compared to observations.')
    
    p4 = doc.add_paragraph('The Radio Occultation (RO) technique provides unique advantages for studying stratospheric GWs, offering global coverage, high vertical resolution (~100 m), and long-term stability', style='CustomBody')
    add_superscript_reference(p4, '13,14')
    p4.add_run('. The nearly two-decade multi-mission RO dataset (2006-2024) from COSMIC-1/2, MetOp, Spire, and other missions represents an unprecedented observational resource for characterizing MJO-GW interactions globally.')
    
    # Proposed Research and Scientific Questions
    heading2 = doc.add_paragraph('Proposed Research and Scientific Questions', style='CustomHeading')
    
    p5 = doc.add_paragraph('This research addresses fundamental questions about stratosphere-troposphere coupling through gravity waves modulated by the MJO. The central scientific questions are:', style='CustomBody')
    
    p6 = doc.add_paragraph('Primary Research Question: How does the MJO modulate stratospheric gravity wave activity globally, and what are the underlying physical mechanisms governing this relationship?', style='CustomBody')
    p6.runs[0].bold = True
    
    p7 = doc.add_paragraph('Specific Scientific Questions:', style='CustomBody')
    p7.runs[0].bold = True
    
    # Add numbered list for scientific questions
    questions = [
        'How do MJO-related changes in tropical convection translate into observable variations in stratospheric GW potential energy, momentum flux, and spectral characteristics?',
        'What is the relative importance of source modulation (MJO convection) versus propagation filtering (MJO-modulated background state) in explaining observed MJO-GW relationships?',
        'How do different MJO propagation types (standing, jumping, slow/fast-propagating) influence the global distribution of stratospheric GW activity?',
        'How well do current climate models represent the observed MJO-GW relationships, and what observational constraints can improve model parameterizations?'
    ]
    
    for i, question in enumerate(questions, 1):
        q_para = doc.add_paragraph(f'{i}. {question}', style='CustomBody')
        q_para.paragraph_format.left_indent = Inches(0.25)
    
    p8 = doc.add_paragraph('The research objectives are structured to address these questions systematically:', style='CustomBody')
    
    # Add objectives
    obj1 = doc.add_paragraph('Objective 1: Validate RO-derived GW Parameters and Develop Momentum Flux Estimation (Months 1-10)', style='CustomBody')
    obj1.runs[0].bold = True
    obj1.add_run('\nEstablish robust methodologies for extracting GW parameters from RO temperature profiles through process-based validation using focused case studies and statistical comparison with independent satellite measurements (TIMED/SABER, Aura/MLS)')
    add_superscript_reference(obj1, '15,16')
    obj1.add_run('. Develop and validate techniques for estimating GW momentum flux from RO data.')
    
    obj2 = doc.add_paragraph('Objective 2: Characterize Global MJO-GW Climatology (Months 8-18)', style='CustomBody')
    obj2.runs[0].bold = True
    obj2.add_run('\nCreate the first comprehensive global climatology of stratospheric GW activity modulated by the MJO using the validated parameters. Categorize MJO events by propagation types following established methodologies')
    add_superscript_reference(obj2, '17')
    obj2.add_run(' and composite corresponding GW anomalies for each type.')
    
    obj3 = doc.add_paragraph('Objective 3: Evaluate Climate Model Representation (Months 15-24)', style='CustomBody')
    obj3.runs[0].bold = True
    obj3.add_run('\nUse the observational climatology as a benchmark to evaluate MJO-GW representation in CMIP6/CMIP7 climate models, analyzing daily 3D GW momentum fluxes and assessing model skill using established metrics')
    add_superscript_reference(obj3, '18')
    obj3.add_run('.')

    # Methodology
    heading3 = doc.add_paragraph('Methodology', style='CustomHeading')

    # Data Acquisition subsection
    subheading1 = doc.add_paragraph('Data Acquisition and Processing', style='CustomBody')
    subheading1.runs[0].bold = True

    p9 = doc.add_paragraph('The methodology integrates advanced satellite data processing, atmospheric dynamics diagnostics, and robust statistical analysis applied to the extensive multi-mission RO dataset (2006-2024) and complementary datasets including ERA5 reanalysis, Real-time Multivariate MJO Index, Outgoing Longwave Radiation data, QBO and ENSO indices, and solar activity indices.', style='CustomBody')

    # GW Parameter Extraction subsection
    subheading2 = doc.add_paragraph('GW Parameter Extraction from RO Profiles', style='CustomBody')
    subheading2.runs[0].bold = True

    p10 = doc.add_paragraph('For each temperature profile T(z), we will:', style='CustomBody')

    # Add methodology steps
    steps = [
        'Estimate background temperature T̄(z) and perturbation T\'(z) = T(z) - T̄(z) using wavelet decomposition¹⁹,²⁰',
        'Calculate buoyancy frequency N²(z) = (g/T̄)(dT̄/dz + g/cₚ)',
        'Calculate GW potential energy per unit mass: Eₚₘ(z) = ½(g/N)²(T\'/T̄)²',
        'Estimate dominant vertical wavelength λz = 2π/|m| using Continuous Wavelet Transform²¹',
        'Estimate directional momentum flux components using: (Fₚₓ, Fₚᵧ) = ρ̄(k,l)/m × Eₚₘ where horizontal wavenumber components are estimated using phase differences between nearby profile pairs²²,²³'
    ]

    for i, step in enumerate(steps, 1):
        step_para = doc.add_paragraph(f'({["i", "ii", "iii", "iv", "v"][i-1]}) {step}', style='CustomBody')
        step_para.paragraph_format.left_indent = Inches(0.25)

    # MJO Composite Analysis subsection
    subheading3 = doc.add_paragraph('MJO Composite Analysis', style='CustomBody')
    subheading3.runs[0].bold = True

    p11 = doc.add_paragraph('GW parameters will be composited by MJO phase (8 phases) and amplitude using the Real-time Multivariate MJO (RMM) index. We will account for the three-dimensional evolution of MJO temperature anomalies and their relationship to GW generation and propagation. Statistical significance will be assessed using bootstrap resampling and field significance tests.', style='CustomBody')

    # Climate Model Evaluation subsection
    subheading4 = doc.add_paragraph('Climate Model Evaluation', style='CustomBody')
    subheading4.runs[0].bold = True

    p12 = doc.add_paragraph('Model evaluation will utilize outputs from CMIP6 and CMIP7, performing MJO composite analysis on daily 3D GW momentum fluxes available from CMIP7 parameterization schemes. Model skill will be assessed using pattern correlation, root-mean-square error, and other established metrics', style='CustomBody')
    add_superscript_reference(p12, '18')
    p12.add_run('.')

    # Relevance to NCAR's Research and Strategic Plan
    heading4 = doc.add_paragraph('Relevance to NCAR\'s Research and Strategic Plan', style='CustomHeading')

    p13 = doc.add_paragraph('This research directly aligns with NCAR\'s strategic priorities in atmospheric science and climate research. The project contributes to NCAR\'s mission of understanding the atmosphere and its interactions with the Earth system through several key areas:', style='CustomBody')

    # NCAR relevance points
    ncar_points = [
        ('Climate Modeling and Prediction:', 'The research addresses critical gaps in climate model representation of stratosphere-troposphere coupling, directly supporting NCAR\'s efforts to improve climate prediction capabilities. The observational constraints developed will inform parameterization improvements in NCAR\'s Community Earth System Model (CESM).'),
        ('Observational Science:', 'The project leverages NCAR\'s expertise in satellite data analysis and atmospheric observations, contributing to the center\'s leadership in observational atmospheric science. The comprehensive RO dataset analysis aligns with NCAR\'s commitment to maximizing the scientific value of observational data.'),
        ('Subseasonal-to-Seasonal Prediction:', 'Understanding MJO-stratosphere interactions is crucial for improving subseasonal-to-seasonal forecasting, a key priority area for NCAR. The research will provide fundamental insights into the physical processes that govern MJO teleconnections and their predictability.'),
        ('Community Service:', 'The resulting datasets and methodologies will be made available to the broader scientific community, supporting NCAR\'s mission of providing scientific resources and tools. The research will contribute to international efforts such as the Stratosphere-troposphere Processes And their Role in Climate (SPARC) initiative.'),
        ('Training and Education:', 'The project will contribute to NCAR\'s educational mission by training the next generation of atmospheric scientists in advanced observational techniques and climate dynamics.')
    ]

    for title, description in ncar_points:
        point_para = doc.add_paragraph(title, style='CustomBody')
        point_para.runs[0].bold = True
        point_para.add_run(f' {description}')

    # Relevance of Previous Research Experience
    heading5 = doc.add_paragraph('Relevance of Previous Research Experience', style='CustomHeading')

    p14 = doc.add_paragraph('The applicant brings extensive expertise in atmospheric gravity wave research using satellite observations, with particular strength in Radio Occultation data analysis and stratospheric dynamics. This background provides an ideal foundation for the proposed research.', style='CustomBody')

    # Previous experience points
    experience_points = [
        ('Radio Occultation Expertise:', 'Previous work has demonstrated proficiency in extracting and analyzing GW parameters from RO temperature profiles, including development of robust quality control procedures and validation techniques²⁴,²⁵. This experience directly translates to the proposed methodology for characterizing MJO-GW interactions.'),
        ('Gravity Wave Research:', 'Extensive research on stratospheric GW activity during various atmospheric phenomena, including sudden stratospheric warmings and long-term variability studies²⁶,²⁷. This background provides deep understanding of GW physics and observational techniques essential for the proposed research.'),
        ('Climate Variability Analysis:', 'Experience in analyzing relationships between GW activity and large-scale atmospheric patterns, including statistical techniques for composite analysis and significance testing. This expertise is directly applicable to characterizing MJO-GW relationships.'),
        ('Multi-satellite Data Integration:', 'Proven ability to work with multiple satellite datasets and cross-validation techniques, essential for the comprehensive approach proposed in this research.'),
        ('International Collaboration:', 'Experience working with international research teams and contributing to global atmospheric science initiatives, supporting the collaborative aspects of this research.')
    ]

    for title, description in experience_points:
        exp_para = doc.add_paragraph(title, style='CustomBody')
        exp_para.runs[0].bold = True
        exp_para.add_run(f' {description}')

    p15 = doc.add_paragraph('The combination of technical expertise in RO data analysis, theoretical understanding of atmospheric dynamics, and experience with climate variability research positions the applicant to successfully execute this ambitious research program and contribute meaningfully to NCAR\'s scientific mission.', style='CustomBody')

    # References
    heading6 = doc.add_paragraph('References', style='CustomHeading')

    # Add sample references with superscript numbers
    references = [
        '1. Zhang, C. Madden-Julian oscillation. Rev. Geophys. 43, RG2003 (2005).',
        '2. Madden, R. A. & Julian, P. R. Observations of the 40–50-day tropical oscillation—a review. Mon. Weather Rev. 122, 814–837 (1994).',
        '3. Guo, Y., Wen, M., Li, T. & Ren, X. Variations in Northern Hemisphere storm track and extratropical cyclone activity associated with the Madden-Julian oscillation. J. Climate 30, 4799–4818 (2017).',
        '4. Baggett, C. F., Lee, S. & Feldstein, S. B. An investigation of the influence of atmospheric rivers on cold-season extratropical cyclones. Mon. Weather Rev. 145, 4019–4034 (2017).',
        '5. Henderson, S. A., Maloney, E. D. & Barnes, E. A. The influence of the Madden-Julian oscillation on Northern Hemisphere winter blocking. J. Climate 29, 4597–4616 (2016).',
        '6. Alexander, M. J. et al. Recent developments in gravity-wave effects in climate models. Q. J. R. Meteorol. Soc. 136, 1103–1124 (2010).',
        '7. Fritts, D. C. & Alexander, M. J. Gravity wave dynamics and effects in the middle atmosphere. Rev. Geophys. 41, 1003 (2003).',
        '8. Hood, L. L., Rossi, S. & Beulen, M. The stratospheric pathway of El Niño–Southern Oscillation influence on the Northern Hemisphere. J. Climate 33, 1451–1468 (2020).',
        '9. Martin, Z. et al. The influence of the quasi-biennial oscillation on the Madden-Julian oscillation. Nat. Rev. Earth Environ. 2, 477–489 (2021).',
        '10. Trencham, C. E. & Hood, L. L. Causes of QBO-MJO connection in climate models. J. Climate 37, 2847–2865 (2024).',
        '11. Hood, L. L. QBO influence on MJO amplitude. Geophys. Res. Lett. 50, e2023GL103321 (2023).',
        '12. Richter, J. H. et al. Progress in simulating the quasi-biennial oscillation in CMIP models. J. Geophys. Res. Atmos. 125, e2019JD032362 (2020).',
        '13. Ho, S.-P. et al. Using radio occultation data for atmospheric studies. Bull. Am. Meteorol. Soc. 103, E2200–E2223 (2022).',
        '14. Tsuda, T., Nishida, M., Rocken, C. & Ware, R. H. A global morphology of gravity wave activity in the stratosphere revealed by the GPS occultation data. J. Geophys. Res. 105, 7257–7273 (2000).',
        '15. Alexander, M. J. & Grimsdell, A. W. Global estimates of gravity wave momentum flux from High Resolution Dynamics Limb Sounder observations. J. Geophys. Res. Atmos. 118, 6988–7007 (2013).',
        '16. Wu, D. L. et al. Global gravity wave variances from Aura MLS. Geophys. Res. Lett. 33, L07809 (2006).',
        '17. Zeng, Z. et al. Structural evolution of the Madden-Julian oscillation from COSMIC radio occultation data. J. Geophys. Res. 117, D22108 (2012).',
        '18. Gerber, E. P. et al. The Dynamics and Variability Model Intercomparison Project (DynVarMIP) for CMIP6. Geosci. Model Dev. 9, 3413–3425 (2016).',
        '19. Alexander, M. J. Interpretations of observed climatological patterns in stratospheric gravity wave variance. J. Geophys. Res. 103, 8627–8640 (1998).',
        '20. Stockwell, R. G., Mansinha, L. & Lowe, R. P. Localization of the complex spectrum: the S transform. IEEE Trans. Signal Process. 44, 998–1001 (1996).',
        '21. Faber, A. et al. Determination of gravity wave momentum flux from GPS radio occultation data. J. Atmos. Sol. Terr. Phys. 96, 47–53 (2013).',
        '22. Schmidt, T., Wickert, J., Beyerle, G. & Heise, S. A climatology of multiple tropopauses derived from GPS radio occultations. Atmos. Chem. Phys. 8, 2777–2796 (2008).',
        '23. Ayorinde, T. T., Pancheva, D. & Mukhtarov, P. Stratospheric gravity wave activity during the 2019 Antarctic sudden stratospheric warming. J. Atmos. Sol. Terr. Phys. 245, 106045 (2024).',
        '24. Ayorinde, T. T. et al. Stratospheric gravity wave potential energy and tropospheric parameters relationships over south america. Earth Planets Space 75, 1–16 (2023).',
        '25. Corwin, K. & Anthes, R. Validation of COSMIC GPS radio occultation data in the troposphere. Atmos. Meas. Tech. 9, 5749–5763 (2016).',
        '26. Li, T. et al. Gravity wave activity in the tropical tropopause layer during the 2009-2010 QBO disruption. J. Geophys. Res. Atmos. 125, e2019JD032006 (2020).',
        '27. Zhou, Q. et al. Observed gravity wave momentum fluxes in the mesosphere and lower thermosphere. J. Geophys. Res. Space Phys. 129, e2023JA032045 (2024).'
    ]

    for ref in references:
        ref_para = doc.add_paragraph(ref, style='CustomBody')
        ref_para.paragraph_format.left_indent = Inches(0.25)
        ref_para.paragraph_format.hanging_indent = Inches(0.25)

    # Save the document
    doc.save('MJO_Proposal_New_Structure.docx')
    print("Word document created successfully: MJO_Proposal_New_Structure.docx")

if __name__ == "__main__":
    create_mjo_proposal()
