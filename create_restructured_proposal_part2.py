#!/usr/bin/env python3
"""
Create the complete restructured MJO proposal with improved visual structure
"""

import docx
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

def add_heading_with_style(doc, text, level=1):
    """Add a heading with consistent styling"""
    heading = doc.add_heading(text, level=level)
    heading.alignment = WD_ALIGN_PARAGRAPH.LEFT
    return heading

def add_bullet_point(doc, text, indent_level=0):
    """Add a bullet point with proper indentation"""
    para = doc.add_paragraph(text, style='List Bullet')
    para.paragraph_format.left_indent = Inches(0.25 * (indent_level + 1))
    return para

def add_numbered_point(doc, text, indent_level=0):
    """Add a numbered point with proper indentation"""
    para = doc.add_paragraph(text, style='List Number')
    para.paragraph_format.left_indent = Inches(0.25 * (indent_level + 1))
    return para

def create_complete_restructured_proposal():
    # Create new document
    doc = Document()
    
    # Title section
    title_para = doc.add_paragraph()
    title_run = title_para.add_run("Proposal Acronym: GRAVMO")
    title_run.bold = True
    title_run.font.size = Pt(14)
    title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    subtitle_para = doc.add_paragraph()
    subtitle_run = subtitle_para.add_run("Modulation of Stratospheric Gravity Waves by the Madden-Julian Oscillation: Observational Evidence from Radio Occultation")
    subtitle_run.bold = True
    subtitle_run.font.size = Pt(12)
    subtitle_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Basic info
    doc.add_paragraph("Type of Action: MSCA Postdoctoral Fellowship (PF-GF)").alignment = WD_ALIGN_PARAGRAPH.CENTER
    doc.add_paragraph("Researcher: Toyese Tunde Ayorinde, Ph.D").alignment = WD_ALIGN_PARAGRAPH.CENTER
    doc.add_paragraph("Supervisor: RNDr. Petr Šácha, Ph.D").alignment = WD_ALIGN_PARAGRAPH.CENTER
    doc.add_paragraph("Host Institution: Charles University, Faculty of Mathematics and Physics, Czech Republic").alignment = WD_ALIGN_PARAGRAPH.CENTER
    doc.add_paragraph("Call: HORIZON-MSCA-202X-PF-01").alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_page_break()
    
    # Abstract
    add_heading_with_style(doc, "Proposal Abstract: GRAVMO", 1)
    
    abstract_text = """Understanding tropical-stratospheric coupling through atmospheric gravity waves (GWs) is crucial for improving subseasonal-to-seasonal forecasts and climate models. The Madden-Julian Oscillation (MJO) modulates tropical GW activity, yet this modulation remains underrepresented in climate models, limiting predictive skill. This project leverages Global Navigation Satellite System Radio Occultation (RO) data to deliver comprehensive, observationally-derived global characterization of stratospheric GWs modulated by the MJO for climate model validation and GW parameterization development."""
    doc.add_paragraph(abstract_text)
    
    # Key Innovation Points
    add_heading_with_style(doc, "Key Innovations", 2)
    innovations = [
        "First systematic global investigation of MJO-modulated stratospheric GWs using nearly two decades of RO data",
        "Novel categorization by MJO propagation types (standing, jumping, slow/fast-propagating)",
        "Comprehensive validation framework combining case studies and statistical comparisons",
        "Direct evaluation of climate model GW parameterizations using observational benchmarks"
    ]
    for point in innovations:
        add_bullet_point(doc, point)
    
    # Research Objectives
    add_heading_with_style(doc, "Research Objectives", 1)
    
    # O1
    add_heading_with_style(doc, "O1. Validation of RO-derived GW Parameters (WP1)", 2)
    o1_points = [
        "Establish comprehensive validation framework for RO observations",
        "Process-based case studies of documented GW events (building on Gupta²⁵ and tropical campaigns²⁶,²⁷)",
        "Statistical validation against TIMED/SABER and Aura/MLS measurements",
        "Develop robust methodologies for GW potential energy and momentum flux estimation"
    ]
    for point in o1_points:
        add_bullet_point(doc, point)
    
    doc.add_paragraph("**Deliverable:** Global climatology of GW parameters with uncertainty estimates, open-access dataset")
    
    # O2
    add_heading_with_style(doc, "O2. MJO-GW Modulation Climatology (WP2)", 2)
    o2_points = [
        "Quantify global and seasonal distribution of MJO's influence on GW field",
        "Categorize MJO events by propagation types and composite corresponding GW anomalies",
        "Analyze three-dimensional evolution of MJO temperature and convective structures",
        "Statistical significance testing of GW activity patterns"
    ]
    for point in o2_points:
        add_bullet_point(doc, point)
    
    doc.add_paragraph("**Deliverable:** Observational climatology of MJO-modulated GW activity by propagation type")
    
    # O3
    add_heading_with_style(doc, "O3. Climate Model Evaluation (WP3)", 2)
    o3_points = [
        "Evaluate CMIP6/7 models' representation of MJO-modulated GWs",
        "Analyze daily 3D GW momentum fluxes from parameterization schemes",
        "Assess performance of different non-orographic GW parameterizations",
        "Identify systematic biases and provide recommendations for improvement"
    ]
    for point in o3_points:
        add_bullet_point(doc, point)
    
    doc.add_paragraph("**Deliverable:** Comprehensive assessment of model capabilities with observational constraints")
    
    # Excellence Section
    add_heading_with_style(doc, "Excellence", 1)
    
    add_heading_with_style(doc, "Scientific Background & Innovation", 2)
    
    # MJO Importance
    add_heading_with_style(doc, "MJO as Key Climate Driver", 3)
    mjo_importance = [
        "Primary driver of tropical intraseasonal variability⁷,⁸",
        "Influences global weather through teleconnections (PNA, NAO patterns)⁵,⁹",
        "Affects storm tracks, cyclones, atmospheric rivers, blocking events¹⁰,¹¹,¹²",
        "Modulated by QBO and ENSO interactions⁵,¹³"
    ]
    for point in mjo_importance:
        add_bullet_point(doc, point)
    
    # GW Role
    add_heading_with_style(doc, "Gravity Waves in Climate System", 3)
    gw_role = [
        "Medium-scale oscillations (horizontal: tens-thousands km, vertical: several km)",
        "Drive Brewer-Dobson Circulation and QBO through momentum transport¹,²",
        "Critical vertical coupling pathway for MJO teleconnections",
        "Generated by MJO-modulated deep convection"
    ]
    for point in gw_role:
        add_bullet_point(doc, point)
    
    # Critical Question
    add_heading_with_style(doc, "Critical Scientific Question", 3)
    doc.add_paragraph("**Key Hypothesis:** Climate models' failure to capture observed QBO-MJO connections⁶,⁷ may result from inadequate representation of MJO-induced GW modulation. Is MJO-modulated GW activity a missing component required for accurate QBO simulation?")
    
    # Methodology Section
    add_heading_with_style(doc, "Methodology", 1)

    # Data Sources Table
    add_heading_with_style(doc, "Data Sources", 2)
    table = doc.add_table(rows=1, cols=3)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.CENTER

    # Header row
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Data Type'
    hdr_cells[1].text = 'Source'
    hdr_cells[2].text = 'Usage'

    # Data rows
    data_sources = [
        ('RO Profiles', 'COSMIC-1/2, MetOp, Spire', 'GW parameter extraction'),
        ('Reanalysis', 'ERA5 daily/monthly', 'Background state, validation'),
        ('Climate Indices', 'RMM, QBO, ENSO indices', 'Event categorization'),
        ('Satellite', 'OLR (NOAA), TIMED/SABER', 'Validation, convection proxy'),
        ('Model Data', 'CMIP6/7 outputs', 'Model evaluation')
    ]

    for data_type, source, usage in data_sources:
        row_cells = table.add_row().cells
        row_cells[0].text = data_type
        row_cells[1].text = source
        row_cells[2].text = usage

    # Work Package Structure
    add_heading_with_style(doc, "Work Package Implementation", 2)

    # WP1
    add_heading_with_style(doc, "WP1: Validation Framework (Months 1-8)", 3)
    wp1_tasks = [
        "**Task 1.1:** RO data processing and quality control",
        "**Task 1.2:** Case study validation with documented GW events",
        "**Task 1.3:** Statistical intercomparison with satellite observations",
        "**Task 1.4:** Uncertainty quantification and methodology optimization"
    ]
    for task in wp1_tasks:
        add_bullet_point(doc, task)

    # WP2
    add_heading_with_style(doc, "WP2: MJO-GW Climatology (Months 6-18)", 3)
    wp2_tasks = [
        "**Task 2.1:** MJO event identification and categorization",
        "**Task 2.2:** Composite analysis by MJO phase and propagation type",
        "**Task 2.3:** Global and seasonal distribution analysis",
        "**Task 2.4:** Statistical significance testing and uncertainty assessment"
    ]
    for task in wp2_tasks:
        add_bullet_point(doc, task)

    # WP3
    add_heading_with_style(doc, "WP3: Model Evaluation (Months 12-24)", 3)
    wp3_tasks = [
        "**Task 3.1:** CMIP6/7 model data processing and analysis",
        "**Task 3.2:** MJO composite analysis on model GW fields",
        "**Task 3.3:** Systematic bias identification and quantification",
        "**Task 3.4:** Performance assessment and recommendations"
    ]
    for task in wp3_tasks:
        add_bullet_point(doc, task)

    # Impact Section
    add_heading_with_style(doc, "Impact", 1)

    add_heading_with_style(doc, "Scientific Impact", 2)
    sci_impact = [
        "**Landmark Dataset:** First global, long-term observational characterization of MJO-modulated GWs",
        "**Model Improvement:** Critical benchmarks for GW parameterization development",
        "**Fundamental Understanding:** New insights into stratosphere-troposphere coupling",
        "**Community Resource:** Open-access datasets for broader scientific use"
    ]
    for point in sci_impact:
        add_bullet_point(doc, point)

    add_heading_with_style(doc, "Broader Impact", 2)
    broader_impact = [
        "Enhanced subseasonal-to-seasonal forecasting capabilities",
        "Improved climate projections for adaptation planning",
        "Benefits for climate-sensitive sectors (agriculture, energy, water)",
        "Reduced economic losses from extreme weather prediction"
    ]
    for point in broader_impact:
        add_bullet_point(doc, point)

    # Implementation Section
    add_heading_with_style(doc, "Implementation", 1)

    add_heading_with_style(doc, "Timeline & Milestones", 2)

    # Timeline table
    timeline_table = doc.add_table(rows=1, cols=4)
    timeline_table.style = 'Table Grid'
    timeline_table.alignment = WD_TABLE_ALIGNMENT.CENTER

    # Header
    timeline_hdr = timeline_table.rows[0].cells
    timeline_hdr[0].text = 'Period'
    timeline_hdr[1].text = 'Work Package'
    timeline_hdr[2].text = 'Key Activities'
    timeline_hdr[3].text = 'Deliverables'

    # Timeline data
    timeline_data = [
        ('Months 1-8', 'WP1', 'Validation framework', 'Validated GW climatology'),
        ('Months 6-18', 'WP2', 'MJO-GW analysis', 'Observational climatology'),
        ('Months 12-24', 'WP3', 'Model evaluation', 'Model assessment report'),
        ('Months 20-24', 'All WPs', 'Synthesis & dissemination', 'Publications & datasets')
    ]

    for period, wp, activities, deliverables in timeline_data:
        row = timeline_table.add_row().cells
        row[0].text = period
        row[1].text = wp
        row[2].text = activities
        row[3].text = deliverables

    add_heading_with_style(doc, "Risk Management", 2)
    risks = [
        "**Data Quality Issues:** Mitigation through multiple validation approaches and quality control",
        "**Computational Resources:** Access to high-performance computing through host institution",
        "**Model Data Availability:** Alternative datasets and model versions as backup",
        "**Timeline Delays:** Flexible task scheduling with overlapping work packages"
    ]
    for risk in risks:
        add_bullet_point(doc, risk)

    return doc

if __name__ == "__main__":
    doc = create_complete_restructured_proposal()
    doc.save('MJO_proposal_TTA_Restructured_Complete.docx')
    print("Created complete restructured proposal: MJO_proposal_TTA_Restructured_Complete.docx")
