from docx import Document
from docx.shared import Pt, Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn

# Helper to set base styles

def set_document_style(doc: Document):
    styles = doc.styles
    for style_name in ["Normal", "Heading 1", "Heading 2", "Heading 3"]:
        st = styles[style_name]
        st.font.name = "Times New Roman"
        st._element.rPr.rFonts.set(qn('w:eastAsia'), 'Times New Roman')
        if style_name == "Normal":
            st.font.size = Pt(10)
        else:
            st.font.size = Pt(11)
    # Page margins
    sec = doc.sections[0]
    sec.top_margin = Inches(1.0)
    sec.bottom_margin = Inches(1.0)
    sec.left_margin = Inches(1.0)
    sec.right_margin = Inches(1.0)


def add_heading(doc: Document, text: str):
    doc.add_heading(text, level=1)


def add_para(doc: Document, text: str, align_left=True):
    p = doc.add_paragraph()
    run = p.add_run(text)
    run.font.name = "Times New Roman"
    run.font.size = Pt(10)
    if align_left:
        p.alignment = WD_ALIGN_PARAGRAPH.LEFT
    return p


def add_cite_subscript(paragraph, label: str):
    # Append subscript numeric citation like 1,2,3 without brackets
    run = paragraph.add_run(label)
    run.font.subscript = True
    run.font.name = "Times New Roman"
    run.font.size = Pt(10)


def add_equation_block(doc: Document, lines):
    # Simple monospaced-like block using paragraph runs; keep TNR for consistency
    for line in lines:
        p = doc.add_paragraph()
        r = p.add_run(line)
        r.font.name = "Times New Roman"
        r.font.size = Pt(10)
        p.alignment = WD_ALIGN_PARAGRAPH.LEFT


def build_doc():
    doc = Document()
    set_document_style(doc)

    # Title (kept minimal to save space)
    title = doc.add_paragraph()
    tr = title.add_run("Modulation of Stratospheric Gravity Waves by the MJO: Observational Benchmark and Model Evaluation")
    tr.bold = True
    tr.font.size = Pt(11)

    # Background and Motivation
    add_heading(doc, "Background and Motivation")
    p = add_para(doc, (
        "The Madden–Julian Oscillation (MJO) is the dominant mode of tropical intraseasonal variability and drives teleconnections "
        "affecting precipitation, storm tracks, blocking, and S2S predictability")
    )
    add_cite_subscript(p, "1–4")
    p.add_run(". Atmospheric gravity waves (GWs) are a key vertical coupling mechanism; they transport momentum and energy upward, "
              "influence the Brewer–Dobson circulation and the quasi-biennial oscillation (QBO), and modulate stratospheric mean flow and variability")
    add_cite_subscript(p, "5–7")
    p.add_run(". Satellite limb and radio occultation (RO) measurements have revealed MJO-related variations in stratospheric GW activity, "
              "yet a unified, global, observationally derived MJO–GW climatology validated against independent sensors and tailored for model evaluation remains missing")
    add_cite_subscript(p, "8–11")
    p.add_run(". Climate models struggle with aspects of MJO variability and its stratospheric pathway, including GW effects and their momentum forcing of the QBO")
    add_cite_subscript(p, "12–14")
    p.add_run(". A robust observational benchmark of MJO-modulated GW potential energy and momentum flux is needed to diagnose parameterization biases and evaluate model fidelity.")

    # Proposed Research and Scientific Questions
    add_heading(doc, "Proposed Research and Scientific Questions")
    p = add_para(doc, (
        "Objective 1 (Process-based validation and methods): Validate RO-derived GW diagnostics against independent satellite sensors and reanalysis-based diagnostics; "
        "establish reliable estimates of GW potential energy (E")
    )
    r = p.add_run("p"); r.font.subscript = True
    p.add_run("), vertical wavelength (λ"); r2 = p.add_run("z"); r2.font.subscript = True; p.add_run(") and momentum-flux proxies suitable for MJO compositing. Key questions include consistency across sensors and uncertainty due to sampling and retrieval assumptions")
    add_cite_subscript(p, "8–10,16")

    p = add_para(doc, (
        "Objective 2 (Global MJO–GW characterization): Quantify how the MJO modulates stratospheric GW activity and structure, stratified by MJO propagation types "
        "(standing, jumping, slow/fast-propagating).")
    )
    add_cite_subscript(p, "4")
    p.add_run(" Key questions: amplitude and vertical-temporal structures by MJO phase/type; background wind and stability filtering; and regional hotspots.")
    # Expand Objective 2 with diagnostics
    add_para(doc, "Diagnostics: Phase-longitude Hovmöller composites for Ep, λz, and GWMF proxies; vertical structure by pressure levels; regional focus over IO–MC–WP and Pacific storm-track entrance.")
    add_para(doc, "Mechanisms: Relate MJO convection/OLR anomalies to background wind/shear changes controlling GW filtering and ducting; assess phase-dependent transmission into the lower stratosphere.")


    p = add_para(doc, (
        "Objective 3 (Model evaluation): Evaluate whether state-of-the-art climate models (CMIP6/7; CESM/WACCM) reproduce observed MJO–GW signatures. "
        "Questions: do parameterized GWs capture observed phase-dependent signals and vertical structure, and can benchmarks identify biases linked to GW schemes and stratospheric mean-flow responses (e.g., QBO forcing)?")
    )
    add_cite_subscript(p, "12–14,17")

    # Methodology
    add_heading(doc, "Methodology")
    add_para(doc, "Datasets: RO profiles (COSMIC-1/2, MetOp, Spire) from CDAAC (2006–present); ERA5 for background state; RMM and MJO type classification; OLR for convection proxies.")

    p = add_para(doc, (
        "Diagnostics: From RO temperature perturbations, compute E")
    )
    r = p.add_run("p"); r.font.subscript = True
    p.add_run(", λ"); r2 = p.add_run("z"); r2.font.subscript = True
    p.add_run(", and momentum-flux proxies using established relationships and, where sampling permits, horizontal-wavelength estimation from phase differencing of adjacent profiles.")
    add_cite_subscript(p, "5,10,11,16")

    p = add_para(doc, (
        "Compositing: Build daily MJO-phase composites of GW diagnostics globally and for key regions; stratify by MJO type and relevant background flow regimes (e.g., lower-stratospheric shear). Validation: cross-compare with SABER/HIRDLS/MLS climatologies; quantify sensitivity and uncertainties.")
    )
    add_cite_subscript(p, "8–11,18")

    # Expanded methodology details for 5–6 pages
    add_para(doc, "Filtering and detrending: Apply vertical polynomial detrending and band-pass filters tuned to stratospheric GW scales (e.g., 2–20 km λz); evaluate sensitivity to filter bandwidth and windowing.")
    add_para(doc, "Uncertainty quantification: Report sampling, retrieval, and filtering uncertainties; propagate to Ep and momentum-flux proxies; provide bootstrap confidence intervals for MJO composites.")
    add_para(doc, "Background-state filtering: Diagnose intrinsic frequency and directional filtering using ERA5 winds and stability; quantify the MJO-phase dependence of GW transmission and critical-level absorption.")
    add_para(doc, "Horizontal wavelength inference: Where profile geometry permits, estimate λh via phase differences between adjacent occultations/gridded profile pairs; cross-check against limb-sounder climatologies.")
    add_para(doc, "Classification: Implement Back et al. (2024) MJO propagation types; test robustness to alternative thresholds and event definitions; maintain transparency via released code and logs.")

    p = add_para(doc, "Model evaluation: Compute analogous diagnostics from CMIP6/7 and CESM(WACCM) outputs, composited by MJO phase/type, and compare amplitudes, vertical structure, and phase-locking against observations.")

    # Equations (inline text blocks)
    add_heading(doc, "Key Equations")
    eq_lines = [
        "Brunt–Väisälä frequency:   N² = (g/θ) · dθ/dz",
        "Vertical wavenumber and wavelength:   m = 2π/λ_z",
        "Temperature perturbation:   T'(z) = T(z) − \u0305T(z)  (band-pass filtered)",
        "Gravity-wave potential energy (per unit mass):   E_p = ½ · (g²/N²) · (T'/T)²",
        "Horizontal wavenumber:   k_h = 2π/λ_h",
        "Proxy for vertical momentum flux (following limb/RO practice):   GWMF ∝ ρ₀ · (N/|m|) · E_p · (k_h/|m|)"
    ]
    add_equation_block(doc, eq_lines)

    # Relevance to NCAR
    add_heading(doc, "Relevance to NCAR’s Research and Strategic Plan")
    p = add_para(doc, "S2S prediction and Earth system modeling: Improves MJO representation and tropical–stratosphere coupling in CESM/WACCM; provides an observation-based benchmark for GW parameterizations and QBO fidelity, supporting CMIP7 and SPARC/DynVarMIP diagnostics.")
    add_cite_subscript(p, "12–14,17")
    add_para(doc, "Open science and community impact: FAIR release of gridded MJO-phase/type composites and code enables broad community use in model development and verification at NCAR and partners.")
    add_para(doc, "NCAR modeling context: Apply diagnostics to CESM2/3(WACCM) historical and AMIP experiments; target analysis of parameterized GW source spectra and orographic/non-orographic partitioning; assess sensitivity to convective schemes influencing MJO convective heating.")
    add_para(doc, "Metrics and deliverables: Provide MJO-phase/type-resolved Ep, λz, and GWMF proxy climatologies (global and regional); publish open datasets and scripts; supply model-ready diagnostics compatible with CMIP tables and SPARC/DynVarMIP recommendations.")

    # Experience
    add_heading(doc, "Relevance of Previous Research Experience")
    add_para(doc, (
        "The PI has extensive experience with satellite-based GW diagnostics (SABER/RO), middle-atmosphere variability, and model–observation comparisons, including validated GW parameter extraction and uncertainty assessment, enabling rapid execution and collaboration with NCAR CESM/WACCM teams.")
    )

    # References (numbered, concise Nature-like style)
    add_heading(doc, "References")
    refs = [
        "Zhang, C. Madden–Julian oscillation. Rev. Geophys. 43, RG2003 (2005).",
        "Madden, R. A. & Julian, P. R. Observations of the 40–50-day tropical oscillation—a review. Mon. Weather Rev. 122, 814–837 (1994).",
        "Henderson, S. A., Maloney, E. D. & Barnes, E. A. MJO influence on NH winter blocking. J. Climate 29, 4597–4616 (2016).",
        "Back, L. E. et al. Process-based classification of MJO propagation types. J. Climate (2024).",
        "Fritts, D. C. & Alexander, M. J. Gravity wave dynamics and effects. Rev. Geophys. 41, 1003 (2003).",
        "Alexander, M. J. Climatological patterns in stratospheric GW variance. J. Geophys. Res. 103, 8627–8640 (1998).",
        "Richter, J. H. et al. Progress in simulating the QBO in CMIP. J. Geophys. Res. Atmos. 125, e2019JD032362 (2020).",
        "Tsuchiya, C. et al. MJO-related intraseasonal variation of GWs (SABER). J. Geophys. Res. Atmos. 121, 8388–8407 (2016).",
        "Alexander, M. J. & Grimsdell, A. W. Global estimates of GW momentum flux (HIRDLS). J. Geophys. Res. Atmos. 118, 6988–7007 (2013).",
        "Faber, A. et al. Determination of GW momentum flux from GPS RO. J. Atmos. Sol. Terr. Phys. 96, 47–53 (2013).",
        "Wu, D. L. et al. Global GW variances from Aura MLS. Geophys. Res. Lett. 33, L07809 (2006).",
        "Son, S.-W. et al. Stratospheric control of the MJO. J. Climate 30, 1909–1922 (2017).",
        "Martin, Z. et al. Influence of the QBO on the MJO. Nat. Rev. Earth Environ. 2, 477–489 (2021).",
        "Trencham, C. E. & Hood, L. L. Causes of weak QBO–MJO connection in some CMIP6 models. J. Climate 37, 2847–2865 (2024).",
        "Ayorinde, T. T. et al. GW activity during the 2019 Antarctic SSW. J. Geophys. Res. Atmos. 128, e2022JD037775 (2023).",
        "Ho, S.-P. et al. Using radio occultation data for atmospheric studies. Bull. Am. Meteorol. Soc. 103, E2200–E2223 (2022).",
        "Gerber, E. P. et al. The DynVarMIP for CMIP6. Geosci. Model Dev. 9, 3413–3425 (2016)."
    ]
    for i, ref in enumerate(refs, start=1):
        p = doc.add_paragraph()
        num = p.add_run(f"{i}. ")
        num.bold = True
        r = p.add_run(ref)
        r.font.name = "Times New Roman"
        r.font.size = Pt(10)

    out_path = "MJO_proposal_NCAR_6pages.docx"
    doc.save(out_path)
    print(f"Saved: {out_path}")


if __name__ == "__main__":
    build_doc()

