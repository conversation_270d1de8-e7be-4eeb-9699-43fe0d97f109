#!/usr/bin/env python3
"""
Extract text content from .docx files to understand structure and content
"""

import sys
import re
from pathlib import Path

def extract_docx_text_simple(docx_path):
    """
    Simple text extraction from docx by reading the XML content
    """
    try:
        import zipfile
        import xml.etree.ElementTree as ET
        
        with zipfile.ZipFile(docx_path, 'r') as docx:
            # Read the main document XML
            xml_content = docx.read('word/document.xml')
            
        # Parse XML and extract text
        root = ET.fromstring(xml_content)
        
        # Define namespace
        ns = {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}
        
        # Extract all text elements
        text_elements = []
        for elem in root.iter():
            if elem.tag.endswith('}t'):  # Text elements
                if elem.text:
                    text_elements.append(elem.text)
        
        return ' '.join(text_elements)
    
    except Exception as e:
        print(f"Error extracting from {docx_path}: {e}")
        return ""

def main():
    # Extract from Interest Statement example
    interest_text = extract_docx_text_simple("Interest_Statement_example_0.docx")
    
    # Extract from MJO proposal
    mjo_text = extract_docx_text_simple("MJO_proposal_TTA_Chronogram_Corrected.docx")
    
    # Save extracted content
    with open("interest_statement_extracted.txt", "w", encoding="utf-8") as f:
        f.write("=== INTEREST STATEMENT EXAMPLE ===\n\n")
        f.write(interest_text[:5000])  # First 5000 chars
    
    with open("mjo_proposal_extracted.txt", "w", encoding="utf-8") as f:
        f.write("=== MJO PROPOSAL CONTENT ===\n\n")
        f.write(mjo_text[:10000])  # First 10000 chars
    
    print("Content extracted to text files")
    
    # Look for reference patterns in MJO proposal
    ref_pattern = r'\d+(?:,\s*\d+)*'  # Pattern for numbered references
    refs = re.findall(ref_pattern, mjo_text)
    print(f"Found potential reference numbers: {refs[:20]}")  # First 20 matches

if __name__ == "__main__":
    main()
