
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta

# Data extracted from the proposal
# Assuming the project starts in July 2025 (M1)

work_packages = [
    {"id": "WP0", "name": "Project Management & Setup", "start_month": 1, "end_month": 6},
    {"id": "WP0.1", "name": "Project initiation", "start_month": 1, "end_month": 1},
    {"id": "WP0.2", "name": "Data acquisition", "start_month": 1, "end_month": 6},
    {"id": "WP0.3", "name": "Setup analysis env", "start_month": 1, "end_month": 2},
    {"id": "WP0.4", "name": "Finalize DMP", "start_month": 6, "end_month": 6},

    {"id": "WP1", "name": "Validation & GW Climatology", "start_month": 3, "end_month": 12},
    {"id": "WP1.1", "name": "Validate RO-derived GW params", "start_month": 3, "end_month": 9},
    {"id": "WP1.2", "name": "Estimate GW Ep and MF", "start_month": 7, "end_month": 12},
    {"id": "WP1.3", "name": "Generate global climatology", "start_month": 10, "end_month": 12},
    {"id": "WP1.4", "name": "Deliverable D1.1", "start_month": 12, "end_month": 12},

    {"id": "WP2", "name": "MJO-Modulated GW Activity Analysis", "start_month": 10, "end_month": 18},
    {"id": "WP2.1", "name": "Produce global climatology of GW activity", "start_month": 10, "end_month": 18},
    {"id": "WP2.2", "name": "Composite GW anomalies by MJO type", "start_month": 10, "end_month": 18},
    {"id": "WP2.3", "name": "Deliverable D2.1", "start_month": 18, "end_month": 18},

    {"id": "WP3", "name": "Climate Model Evaluation", "start_month": 16, "end_month": 22},
    {"id": "WP3.1", "name": "Evaluate CMIP6/7 models", "start_month": 16, "end_month": 22},
    {"id": "WP3.2", "name": "Assess MJO-GW momentum flux representation", "start_month": 16, "end_month": 22},
    {"id": "WP3.3", "name": "Deliverable D3.1", "start_month": 22, "end_month": 22},

    {"id": "WP4", "name": "Dissemination, Exploitation & Reporting", "start_month": 15, "end_month": 24},
    {"id": "WP4.1", "name": "Prepare & submit manuscripts", "start_month": 15, "end_month": 24},
    {"id": "WP4.2", "name": "Present at conferences", "start_month": 18, "end_month": 24},
    {"id": "WP4.3", "name": "Archive data products", "start_month": 22, "end_month": 24},
    {"id": "WP4.4", "name": "Complete final reporting", "start_month": 24, "end_month": 24},
]

project_start_date = datetime(2025, 7, 1) # Assuming July 2025 is Month 1

fig, ax = plt.subplots(figsize=(16, 10)) # Increased width to 16

tasks = []
start_dates = []
end_dates = []

# Define a color palette for main WPs and sub-tasks
main_wp_colors = ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd"]
sub_task_colors = ["#8ecdf2", "#ffbf80", "#98e098", "#ff9c9c", "#c7b2d9"]

wp_color_map = {
    "WP0": main_wp_colors[0],
    "WP1": main_wp_colors[1],
    "WP2": main_wp_colors[2],
    "WP3": main_wp_colors[3],
    "WP4": main_wp_colors[4],
}

for wp in work_packages:
    start_date = project_start_date + timedelta(days=(wp["start_month"] - 1) * 30)
    end_date = project_start_date + timedelta(days=(wp["end_month"] * 30) -1)
    duration = end_date - start_date

    tasks.append(f"{wp['id']}: {wp['name']}")

    current_color = ""
    if len(wp["id"]) == 3: # Main Work Package (e.g., WP0, WP1)
        current_color = wp_color_map[wp["id"]]
        ax.barh(tasks[-1], duration, left=start_date, height=0.6, align='center', color=current_color)
    else: # Sub-task (e.g., WP0.1, WP1.1)
        main_wp_id = wp["id"][:3]
        # Find the index of the main WP color to get the corresponding sub-task color
        main_wp_index = list(wp_color_map.keys()).index(main_wp_id)
        current_color = sub_task_colors[main_wp_index]
        ax.barh(tasks[-1], duration, left=start_date, height=0.4, align='center', color=current_color)

# Formatting the plot
ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2)) # Show every 2 months
ax.xaxis.set_major_formatter(mdates.DateFormatter('%b %Y'))
ax.xaxis.set_minor_locator(mdates.MonthLocator())

plt.xlabel('Timeline')
plt.title('Project Workflow Detailed Gantt Chart')
plt.gca().invert_yaxis() # To display tasks from top to bottom
#plt.grid(True, linestyle='--', alpha=0.7)

# Add footnote for DMP
plt.figtext(0.02, 0.02, "*DMP: Data Management Plan", ha="left", fontsize=9, color="gray")

plt.tight_layout()

# Save the chart
# Save the chart
plt.savefig('D:\MJO_stratospheric_waves/detailed_gantt_chart_wider.jpeg')


