#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to modify the MJO proposal document by:
1. <PERSON><PERSON> et al. (2022) citation
2. Adding tropical GW observation references to O1
3. Updating reference numbering
"""

import docx
from docx import Document
import re

def modify_proposal():
    # Load the document
    doc = Document('MJO_proposal_TTA_Chronogram_Cor_TTA - Copy.docx')

    # Define the new Bushell reference
    bushell_ref = "Bushell, A. C., Anstey, J. <PERSON>., <PERSON>, N., Kawatani, Y., Osprey, S. M., <PERSON>, J. <PERSON>, ... & Yu<PERSON>oto, S. (2022). Evaluation of the quasi‐biennial oscillation in global climate models for the SPARC QBO‐initiative. Quarterly Journal of the Royal Meteorological Society, 148(744), 1459-1489."

    # Define tropical GW references to add
    tropical_gw_refs = [
        "Alexander, M. <PERSON>, <PERSON>, <PERSON>, <PERSON>, C., Polavarapu, S., <PERSON>, P., <PERSON>, F., ... & <PERSON>, S. (2010). Recent developments in gravity‐wave effects in climate models and the global distribution of gravity‐wave momentum flux from observations and models. Quarterly Journal of the Royal Meteorological Society, 136(650), 1103-1124.",
        "<PERSON><PERSON>, <PERSON>, <PERSON>, P., <PERSON>e, J. C., Hepplewhite, C. L., Mlynczak, M. G., Russell III, J. M., & Riese, M. (2011). Implications for atmospheric dynamics derived from global observations of gravity wave momentum flux in stratosphere and mesosphere. Journal of Geophysical Research: Atmospheres, 116(D19)."
    ]

    # Track changes made
    changes_made = []

    # First, let's examine all paragraphs to understand the structure
    print("Examining document structure...")
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if text:
            if "fundamental weakness" in text:
                print(f"Para {i+1}: Found target sentence: {text[:100]}...")
            elif "Building on the event analysis by Gupta" in text:
                print(f"Para {i+1}: Found O1 paragraph: {text[:100]}...")
            elif text.startswith(("1.", "2.", "3.", "4.", "5.", "6.", "7.", "8.", "9.")):
                print(f"Para {i+1}: Reference: {text[:50]}...")

    # Process each paragraph
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text

        # Look for the specific sentence about QBO-MJO connection
        if "fundamental weakness⁶" in text:
            # Insert the Bushell citation after "Moreover, the climate models struggle to represent correctly the QBO itself."
            new_text = text.replace(
                "Moreover, the climate models struggle to represent correctly the QBO itself.",
                "Moreover, the climate models struggle to represent correctly the QBO itself⁷."
            )
            # Update the paragraph text
            paragraph.clear()
            paragraph.add_run(new_text)
            changes_made.append(f"Added Bushell citation to paragraph {i+1}")

        # Look for the O1 objective paragraph to add tropical GW references
        elif "Building on the event analysis by Gupta25" in text or "Building on the event analysis by Gupta²⁵" in text:
            # Add tropical GW observation references
            if "Gupta25" in text:
                new_text = text.replace(
                    "Building on the event analysis by Gupta25",
                    "Building on the event analysis by Gupta25 and tropical GW observation campaigns26, 27"
                )
            else:
                new_text = text.replace(
                    "Building on the event analysis by Gupta²⁵",
                    "Building on the event analysis by Gupta²⁵ and tropical GW observation campaigns²⁶, ²⁷"
                )
            paragraph.clear()
            paragraph.add_run(new_text)
            changes_made.append(f"Added tropical GW references to O1 in paragraph {i+1}")

    # Now add the references to the reference section
    print("\nAdding references to reference section...")

    # Add references at the end of the document
    # Add Bushell reference
    bushell_para = doc.add_paragraph()
    bushell_para.text = f"7. {bushell_ref}"
    changes_made.append("Added Bushell reference as #7")

    # Add tropical GW references
    ref26_para = doc.add_paragraph()
    ref26_para.text = f"26. {tropical_gw_refs[0]}"

    ref27_para = doc.add_paragraph()
    ref27_para.text = f"27. {tropical_gw_refs[1]}"

    changes_made.append("Added tropical GW references as #26 and #27")

    # Save the modified document
    output_filename = 'MJO_proposal_TTA_Chronogram_Cor_TTA_Modified.docx'
    doc.save(output_filename)

    print(f"Document modified and saved as: {output_filename}")
    print("Changes made:")
    for change in changes_made:
        print(f"  - {change}")

    return output_filename

if __name__ == "__main__":
    modify_proposal()
