#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to properly fix the MJO proposal document
"""

import docx
from docx import Document

def fix_proposal():
    # Load the original document
    doc = Document('MJO_proposal_TTA_Chronogram_Cor_TTA - Copy.docx')
    
    # Track changes made
    changes_made = []
    
    # Process each paragraph
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text
        
        # Fix the QBO-MJO sentence
        if "Importantly, the failure of many climate models to capture the observed QBO-MJO connection may be indicative of a more fundamental weakness⁶" in text:
            new_text = text.replace(
                "Importantly, the failure of many climate models to capture the observed QBO-MJO connection may be indicative of a more fundamental weakness⁶. Moreover, the climate models struggle to represent correctly the QBO itself.",
                "Importantly, the failure of many climate models to capture the observed QBO-MJO connection may be indicative of a more fundamental weakness⁶. Moreover, the climate models struggle to represent correctly the QBO itself⁷."
            )
            paragraph.clear()
            paragraph.add_run(new_text)
            changes_made.append(f"Added Bushell citation (⁷) to paragraph {i+1}")
            
        # Fix the O1 objective paragraph
        elif "Building on the event analysis by <PERSON><PERSON>" in text:
            new_text = text.replace(
                "Building on the event analysis by <PERSON><PERSON>",
                "Building on the event analysis by Gupta25 and tropical GW observation campaigns26, 27"
            )
            paragraph.clear()
            paragraph.add_run(new_text)
            changes_made.append(f"Added tropical GW references (26, 27) to O1 in paragraph {i+1}")
    
    # Add new references at the end
    bushell_ref = "7. Bushell, A. C., Anstey, J. A., Butchart, N., Kawatani, Y., Osprey, S. M., Richter, J. H., ... & Yukimoto, S. (2022). Evaluation of the quasi‐biennial oscillation in global climate models for the SPARC QBO‐initiative. Quarterly Journal of the Royal Meteorological Society, 148(744), 1459-1489."
    
    tropical_gw_refs = [
        "26. Alexander, M. J., Geller, M., McLandress, C., Polavarapu, S., Preusse, P., Sassi, F., ... & Watanabe, S. (2010). Recent developments in gravity‐wave effects in climate models and the global distribution of gravity‐wave momentum flux from observations and models. Quarterly Journal of the Royal Meteorological Society, 136(650), 1103-1124.",
        "27. Ern, M., Preusse, P., Gille, J. C., Hepplewhite, C. L., Mlynczak, M. G., Russell III, J. M., & Riese, M. (2011). Implications for atmospheric dynamics derived from global observations of gravity wave momentum flux in stratosphere and mesosphere. Journal of Geophysical Research: Atmospheres, 116(D19)."
    ]
    
    # Add the new references
    doc.add_paragraph(bushell_ref)
    doc.add_paragraph(tropical_gw_refs[0])
    doc.add_paragraph(tropical_gw_refs[1])
    
    changes_made.append("Added Bushell reference as #7")
    changes_made.append("Added tropical GW references as #26 and #27")
    
    # Save the corrected document
    output_filename = 'MJO_proposal_TTA_Chronogram_Cor_TTA_Fixed.docx'
    doc.save(output_filename)
    
    print(f"Document fixed and saved as: {output_filename}")
    print("Changes made:")
    for change in changes_made:
        print(f"  - {change}")
    
    return output_filename

if __name__ == "__main__":
    fix_proposal()
